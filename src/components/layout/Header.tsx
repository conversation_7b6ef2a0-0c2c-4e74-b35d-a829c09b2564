'use client';

import { useState, useEffect } from 'react';
import { Search, Menu, X } from 'lucide-react';

export default function Header() {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Close search modal on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsSearchOpen(false);
      }
    };

    if (isSearchOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isSearchOpen]);

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b border-gray-200 bg-white/80 backdrop-blur-md">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <div className="flex items-center">
              <a href="/" className="text-2xl font-bold text-gray-900">
                Zento
              </a>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-700 hover:text-gray-900 transition-colors">
                Home
              </a>
              <a href="/categories" className="text-gray-700 hover:text-gray-900 transition-colors">
                Categories
              </a>
              <a href="/about" className="text-gray-700 hover:text-gray-900 transition-colors">
                About
              </a>
              <a href="/contact" className="text-gray-700 hover:text-gray-900 transition-colors">
                Contact
              </a>
            </nav>

            {/* Search and Mobile Menu */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsSearchOpen(true)}
                className="p-2 text-gray-700 hover:text-gray-900 transition-colors"
                aria-label="Search"
              >
                <Search className="h-5 w-5" />
              </button>

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden p-2 text-gray-700 hover:text-gray-900 transition-colors"
                aria-label="Toggle menu"
              >
                {isMobileMenuOpen ? (
                  <X className="h-5 w-5" />
                ) : (
                  <Menu className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <div className="md:hidden border-t border-gray-200 py-4">
              <nav className="flex flex-col space-y-4">
                <a href="/" className="text-gray-700 hover:text-gray-900 transition-colors">
                  Home
                </a>
                <a href="/categories" className="text-gray-700 hover:text-gray-900 transition-colors">
                  Categories
                </a>
                <a href="/about" className="text-gray-700 hover:text-gray-900 transition-colors">
                  About
                </a>
                <a href="/contact" className="text-gray-700 hover:text-gray-900 transition-colors">
                  Contact
                </a>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Search Modal */}
      {isSearchOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm animate-fade-in">
          <div className="flex min-h-full items-start justify-center p-4 pt-16">
            <div className="w-full max-w-2xl bg-white rounded-lg shadow-xl animate-slide-in-top">
              <div className="flex items-center p-4 border-b border-gray-200">
                <Search className="h-5 w-5 text-gray-400 mr-3" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search articles..."
                  className="flex-1 text-lg outline-none"
                  autoFocus
                />
                <button
                  onClick={() => {
                    setIsSearchOpen(false);
                    setSearchQuery('');
                  }}
                  className="ml-3 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="p-4">
                {searchQuery ? (
                  <div>
                    <p className="text-gray-500 text-sm mb-3">Search results for "{searchQuery}":</p>
                    <div className="space-y-2">
                      <div className="p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                        <h4 className="font-medium text-gray-900">Sample Article Title</h4>
                        <p className="text-sm text-gray-600">Brief description of the article...</p>
                      </div>
                      <div className="p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                        <h4 className="font-medium text-gray-900">Another Article</h4>
                        <p className="text-sm text-gray-600">Another brief description...</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">Start typing to search articles...</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
