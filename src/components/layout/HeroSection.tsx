import Image from 'next/image';
import { <PERSON><PERSON><PERSON>, Github, Twitter, Linkedin } from 'lucide-react';

export default function HeroSection() {
  return (
    <section className="py-16 lg:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* Profile Image */}
          <div className="mb-8">
            <div className="relative w-32 h-32 mx-auto mb-6 animate-fade-in-up">
              <div className="w-32 h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-4xl font-bold">
                JD
              </div>
            </div>
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6 text-balance">
            Hi, I'm <PERSON> 👋
          </h1>

          {/* Description */}
          <p className="text-xl lg:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto text-balance">
            I'm a <strong className="text-gray-900">design technologist</strong> in Atlanta. 
            I like working on the front-end of the web. This is my site, <strong className="text-gray-900">Zento</strong> where 
            I blog, share and write tutorials…
          </p>

          {/* Social Links */}
          <div className="flex items-center justify-center space-x-6 mb-8">
            <a
              href="#"
              className="text-gray-600 hover:text-gray-900 transition-colors"
              aria-label="Twitter"
            >
              <Twitter className="h-6 w-6" />
            </a>
            <a
              href="#"
              className="text-gray-600 hover:text-gray-900 transition-colors"
              aria-label="GitHub"
            >
              <Github className="h-6 w-6" />
            </a>
            <a
              href="#"
              className="text-gray-600 hover:text-gray-900 transition-colors"
              aria-label="LinkedIn"
            >
              <Linkedin className="h-6 w-6" />
            </a>
          </div>

          {/* Call to Action Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <button className="inline-flex items-center px-6 py-3 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors font-medium">
              Let's connect
              <ArrowRight className="ml-2 h-4 w-4" />
            </button>
            <button className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium">
              Get Started
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
